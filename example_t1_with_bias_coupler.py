# -*- coding: utf-8 -*-
"""
T1WithBiasCoupler实验使用示例

这个实验用于研究在coupler上施加z_amp对目标比特T1时间的影响。
相比于T1Extend，这个实验专注于coupler的直接z_amp影响，而不涉及频率转换。
"""

from pyQCat.experiments.single.error_quantification.t1_with_bias_coupler import T1WithBiasCoupler
from pyQCat.tools import qarange

def example_usage():
    """T1WithBiasCoupler实验使用示例"""
    
    # 假设我们有一个实验上下文
    # context = your_experiment_context
    
    # 创建T1WithBiasCoupler实验实例
    # t1_exp = T1WithBiasCoupler.from_experiment_context(context)
    
    # 设置实验选项
    experiment_options = {
        # T1基础选项
        'delays': qarange(200, 30000, 300),  # T1延时扫描范围
        'z_amp': None,  # 目标比特的z_amp，通常设为None或0
        
        # T1WithBiasCoupler特有选项
        'tq_name': 'q0',  # 目标比特名称
        'bias_coupler_name_list': ['c0-1', 'c1-2'],  # 要施加bias的coupler列表
        'bias_coupler_z_amp_list': [0.1, 0.2],  # 对应coupler的z_amp值
    }
    
    # 设置分析选项
    analysis_options = {
        'quality_bounds': [0.9, 0.85, 0.77],  # 拟合质量阈值
        'is_plot': True,  # 是否绘图
        'save_result': True,  # 是否保存结果
    }
    
    print("T1WithBiasCoupler实验配置示例:")
    print("=" * 50)
    print("实验目的: 研究coupler z_amp对比特T1时间的影响")
    print(f"目标比特: {experiment_options['tq_name']}")
    print(f"Bias Coupler列表: {experiment_options['bias_coupler_name_list']}")
    print(f"对应Z_amp值: {experiment_options['bias_coupler_z_amp_list']}")
    print(f"T1延时扫描: {experiment_options['delays']}")
    print("=" * 50)
    
    # 实际运行实验的代码（需要真实的实验环境）
    # t1_exp.set_experiment_options(**experiment_options)
    # t1_exp.set_analysis_options(**analysis_options)
    # result = t1_exp.run()
    
    return experiment_options, analysis_options

def compare_with_t1extend():
    """与T1Extend的对比说明"""
    
    print("\nT1WithBiasCoupler vs T1Extend 对比:")
    print("=" * 60)
    print("T1Extend特点:")
    print("- 支持频率到幅值的转换 (bq_freq_list)")
    print("- 支持自动设置coupler z_amp (auto_set_coupler_zamp)")
    print("- 复杂的频率转换逻辑")
    print("- 适用于需要精确频率控制的场景")
    print()
    print("T1WithBiasCoupler特点:")
    print("- 直接使用z_amp值，无频率转换")
    print("- 专注于coupler z_amp对比特的直接影响")
    print("- 简化的配置和逻辑")
    print("- 适用于研究coupler bias效应的场景")
    print("=" * 60)

if __name__ == "__main__":
    example_usage()
    compare_with_t1extend()
